#!/bin/bash

# PodSail Application Startup Script
# This script includes the necessary JVM arguments to fix CloudPods Java SDK module access issues

# Set default profile if not provided
PROFILE=${1:-local}

# JVM arguments to fix module access issues with CloudPods SDK
JVM_ARGS="--add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED"

# Additional JVM arguments for performance and debugging (optional)
ADDITIONAL_JVM_ARGS="-Xms512m -Xmx2g -XX:+UseG1GC"

echo "Starting PodSail application with profile: $PROFILE"
echo "JVM Arguments: $JVM_ARGS $ADDITIONAL_JVM_ARGS"

# Run the application
java $JVM_ARGS $ADDITIONAL_JVM_ARGS -Dspring.profiles.active=$PROFILE -jar target/server.jar
