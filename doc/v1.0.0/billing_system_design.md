# 云服务器计费系统设计文档

## 概述

本文档描述了支持按需付费和包年包月两种计费方式的云服务器计费系统设计。系统基于现有的用户管理和余额管理功能，扩展了完整的计费、订单、实例管理和续费功能。

## 核心表结构

### 1. server_sku_billing_method - 主机套餐规格定价表

这是核心的定价配置表，支持同一个SKU配置多种计费方式。

**关键字段说明：**
- `billing_type`: 计费类型
  - 1: 按需付费(postpaid) - 用完再付费
  - 2: 包年包月(prepaid) - 预付费
- `billing_cycle`: 计费周期
  - 按需付费: `1H`(按小时), `1D`(按天)
  - 包年包月: `1M`(1个月), `3M`(3个月), `6M`(6个月), `1Y`(1年), `2Y`(2年), `3Y`(3年)
- `price`: 实际销售价格
- `original_price`: 原价，用于显示优惠信息
- `discount_rate`: 折扣率，0.8表示8折

**定价策略：**
- 按需付费通常单价较高，但灵活性好
- 包年包月预付费，时间越长折扣越大
- 支持区域和可用区差异化定价

### 2. user_order - 用户订单表

记录用户的购买订单，支持两种计费方式的订单管理。

**关键字段说明：**
- `billing_method_id`: 关联具体的计费方式配置
- `quantity`: 购买数量，支持批量购买
- `total_amount`: 订单总金额
- `discount_amount`: 优惠金额
- `actual_amount`: 实际支付金额
- `order_status`: 订单状态流转
- `pay_status`: 支付状态管理

### 3. user_server_instance - 用户服务器实例表

管理用户的服务器实例，记录计费相关信息。

**关键字段说明：**
- `billing_type` & `billing_cycle`: 实例的计费方式
- `start_time`: 开始计费时间
- `end_time`: 结束计费时间（按需付费实例销毁时设置）
- `expire_time`: 到期时间（包年包月实例）
- `auto_renew`: 自动续费标识

### 4. billing_record - 计费记录表

详细记录每次计费的明细，用于账单生成和对账。

**关键字段说明：**
- `duration`: 计费时长（秒）
- `record_type`: 记录类型
  - 1: 正常计费
  - 2: 退费
  - 3: 补费
- `settlement_status`: 结算状态，用于财务对账

### 5. renewal_record - 续费记录表

记录包年包月实例的续费历史。

**关键字段说明：**
- `renewal_type`: 续费类型
  - 1: 手动续费
  - 2: 自动续费
- `original_expire_time` & `new_expire_time`: 续费前后的到期时间

## 计费流程

### 按需付费流程

1. **创建订单**: 用户选择按需付费方式，创建订单
2. **创建实例**: 订单支付后，创建服务器实例，记录开始计费时间
3. **定时计费**: 系统定时（如每小时）生成计费记录，从用户余额扣费
4. **实例销毁**: 用户销毁实例时，记录结束计费时间，生成最后一次计费记录

### 包年包月流程

1. **创建订单**: 用户选择包年包月方式，创建预付费订单
2. **支付订单**: 用户一次性支付整个周期的费用
3. **创建实例**: 创建服务器实例，设置到期时间
4. **生成计费记录**: 创建一条覆盖整个周期的计费记录
5. **续费管理**: 到期前提醒续费，支持自动续费

## 定价策略示例

以2核4G套餐为例：

| 计费方式 | 周期 | 原价 | 实际价格 | 折扣率 | 说明 |
|---------|------|------|----------|--------|------|
| 按需付费 | 1小时 | 0.50元 | 0.50元 | 100% | 灵活使用 |
| 按需付费 | 1天 | 12.00元 | 10.00元 | 83.3% | 按天有小幅优惠 |
| 包年包月 | 1个月 | 360元 | 300元 | 83.3% | 包月基础折扣 |
| 包年包月 | 3个月 | 1080元 | 850元 | 78.7% | 包季度额外优惠 |
| 包年包月 | 6个月 | 2160元 | 1600元 | 74.1% | 包半年更大优惠 |
| 包年包月 | 1年 | 4320元 | 3000元 | 69.4% | 包年最大优惠 |
| 包年包月 | 2年 | 8640元 | 5500元 | 63.7% | 长期使用优惠 |
| 包年包月 | 3年 | 12960元 | 8000元 | 61.7% | 超长期最优惠 |

## 系统特性

### 1. 灵活的定价配置
- 支持同一SKU多种计费方式
- 支持区域和时间差异化定价
- 支持灵活的折扣策略

### 2. 完整的订单管理
- 支持订单状态流转
- 支持支付状态管理
- 支持退款和取消

### 3. 精确的计费记录
- 按秒级精度计费
- 支持补费和退费
- 完整的审计日志

### 4. 智能续费管理
- 支持自动续费
- 到期提醒
- 续费历史记录

### 5. 财务对账支持
- 详细的计费明细
- 结算状态管理
- 支持财务报表生成

## 扩展性考虑

### 1. 多资源类型支持
当前设计主要针对服务器实例，可以扩展支持：
- 云硬盘
- 网络带宽
- 负载均衡器
- 数据库实例

### 2. 更复杂的定价模型
- 阶梯定价
- 组合套餐定价
- 动态定价

### 3. 多币种支持
- 支持多种货币
- 汇率转换
- 区域化定价

### 4. 优惠券和促销
- 优惠券系统
- 促销活动
- 会员折扣

## 注意事项

1. **时区处理**: 所有时间字段使用UTC时间戳，前端显示时转换为用户时区
2. **精度处理**: 金额字段使用decimal(20,4)，支持4位小数精度
3. **并发控制**: 计费和扣费操作需要考虑并发控制，避免重复扣费
4. **数据一致性**: 订单、实例、计费记录之间的数据一致性保证
5. **性能优化**: 大量计费记录的查询性能优化，考虑分表分库策略
