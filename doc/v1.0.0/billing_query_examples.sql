# 计费系统常用查询示例

# 1. 查询某个SKU的所有计费方式和价格
SELECT 
    sbm.server_sku_id,
    CASE sbm.billing_type 
        WHEN 1 THEN '按需付费' 
        WHEN 2 THEN '包年包月' 
    END as billing_type_name,
    sbm.billing_cycle,
    sbm.price,
    sbm.original_price,
    sbm.discount_rate,
    CONCAT(ROUND((1 - sbm.discount_rate) * 100, 1), '%') as discount_percent,
    sbm.currency,
    sbm.status
FROM server_sku_billing_method sbm
WHERE sbm.server_sku_id = 'ecs.s6-c1m2.large'
  AND sbm.status = 1
  AND (sbm.expire_time = 0 OR sbm.expire_time > UNIX_TIMESTAMP())
ORDER BY sbm.billing_type,
         CASE sbm.billing_cycle
             WHEN '1H' THEN 1
             WHEN '1D' THEN 2
             WHEN '1M' THEN 3
             WHEN '3M' THEN 4
             WHEN '6M' THEN 5
             WHEN '1Y' THEN 6
             WHEN '2Y' THEN 7
             WHEN '3Y' THEN 8
         END;

# 2. 查询用户的所有订单及状态
SELECT 
    uo.order_no,
    uo.server_sku_id,
    CASE uo.billing_type 
        WHEN 1 THEN '按需付费' 
        WHEN 2 THEN '包年包月' 
    END as billing_type_name,
    uo.billing_cycle,
    uo.quantity,
    uo.unit_price,
    uo.total_amount,
    uo.discount_amount,
    uo.actual_amount,
    uo.currency,
    CASE uo.order_status 
        WHEN 1 THEN '待支付' 
        WHEN 2 THEN '已支付' 
        WHEN 3 THEN '已取消' 
        WHEN 4 THEN '已退款' 
    END as order_status_name,
    CASE uo.pay_status 
        WHEN 1 THEN '未支付' 
        WHEN 2 THEN '已支付' 
        WHEN 3 THEN '支付失败' 
    END as pay_status_name,
    FROM_UNIXTIME(uo.created_at) as order_time,
    FROM_UNIXTIME(uo.pay_time) as pay_time,
    uo.remark
FROM user_order uo
WHERE uo.user_id = 1
  AND uo.is_del = 0
ORDER BY uo.created_at DESC;

# 3. 查询用户的所有服务器实例及计费信息
SELECT 
    usi.server_id,
    usi.server_name,
    usi.server_sku_id,
    CASE usi.billing_type 
        WHEN 1 THEN '按需付费' 
        WHEN 2 THEN '包年包月' 
    END as billing_type_name,
    usi.billing_cycle,
    CASE usi.instance_status 
        WHEN 1 THEN '创建中' 
        WHEN 2 THEN '运行中' 
        WHEN 3 THEN '已停止' 
        WHEN 4 THEN '已销毁' 
    END as instance_status_name,
    FROM_UNIXTIME(usi.start_time) as start_time,
    CASE 
        WHEN usi.end_time = 0 THEN '运行中' 
        ELSE FROM_UNIXTIME(usi.end_time) 
    END as end_time,
    CASE 
        WHEN usi.expire_time = 0 THEN '无到期时间' 
        ELSE FROM_UNIXTIME(usi.expire_time) 
    END as expire_time,
    CASE usi.auto_renew 
        WHEN 0 THEN '否' 
        WHEN 1 THEN '是' 
    END as auto_renew_name,
    usi.region_id,
    usi.zone_id
FROM user_server_instance usi
WHERE usi.user_id = 1
  AND usi.is_del = 0
ORDER BY usi.created_at DESC;

# 4. 查询用户的计费明细
SELECT 
    br.server_id,
    usi.server_name,
    CASE br.billing_type 
        WHEN 1 THEN '按需付费' 
        WHEN 2 THEN '包年包月' 
    END as billing_type_name,
    br.billing_cycle,
    FROM_UNIXTIME(br.start_time) as billing_start_time,
    FROM_UNIXTIME(br.end_time) as billing_end_time,
    CONCAT(ROUND(br.duration / 3600, 2), ' 小时') as duration_hours,
    br.unit_price,
    br.amount,
    br.currency,
    CASE br.record_type 
        WHEN 1 THEN '正常计费' 
        WHEN 2 THEN '退费' 
        WHEN 3 THEN '补费' 
    END as record_type_name,
    CASE br.settlement_status 
        WHEN 1 THEN '未结算' 
        WHEN 2 THEN '已结算' 
    END as settlement_status_name,
    FROM_UNIXTIME(br.created_at) as billing_time,
    br.remark
FROM billing_record br
LEFT JOIN user_server_instance usi ON br.instance_id = usi.id
WHERE br.user_id = 1
  AND br.is_del = 0
ORDER BY br.start_time DESC;

# 5. 查询用户的续费记录
SELECT 
    rr.instance_id,
    usi.server_name,
    usi.server_id,
    CASE rr.renewal_type 
        WHEN 1 THEN '手动续费' 
        WHEN 2 THEN '自动续费' 
    END as renewal_type_name,
    rr.renewal_cycle,
    rr.renewal_amount,
    FROM_UNIXTIME(rr.original_expire_time) as original_expire_time,
    FROM_UNIXTIME(rr.new_expire_time) as new_expire_time,
    CASE rr.renewal_status 
        WHEN 1 THEN '续费中' 
        WHEN 2 THEN '续费成功' 
        WHEN 3 THEN '续费失败' 
    END as renewal_status_name,
    FROM_UNIXTIME(rr.renewal_time) as renewal_time,
    rr.remark
FROM renewal_record rr
LEFT JOIN user_server_instance usi ON rr.instance_id = usi.id
WHERE rr.user_id = 1
  AND rr.is_del = 0
ORDER BY rr.renewal_time DESC;

# 6. 统计用户的消费情况
SELECT 
    DATE_FORMAT(FROM_UNIXTIME(br.start_time), '%Y-%m') as billing_month,
    CASE br.billing_type 
        WHEN 1 THEN '按需付费' 
        WHEN 2 THEN '包年包月' 
    END as billing_type_name,
    COUNT(*) as billing_count,
    SUM(br.amount) as total_amount,
    br.currency
FROM billing_record br
WHERE br.user_id = 1
  AND br.is_del = 0
  AND br.record_type = 1  -- 只统计正常计费
  AND br.start_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 12 MONTH))
GROUP BY DATE_FORMAT(FROM_UNIXTIME(br.start_time), '%Y-%m'), br.billing_type, br.currency
ORDER BY billing_month DESC, br.billing_type;

# 7. 查询即将到期的包年包月实例（7天内到期）
SELECT 
    usi.user_id,
    u.username,
    usi.server_id,
    usi.server_name,
    usi.server_sku_id,
    usi.billing_cycle,
    FROM_UNIXTIME(usi.expire_time) as expire_time,
    ROUND((usi.expire_time - UNIX_TIMESTAMP()) / 86400, 1) as days_to_expire,
    CASE usi.auto_renew 
        WHEN 0 THEN '否' 
        WHEN 1 THEN '是' 
    END as auto_renew_name,
    CASE usi.instance_status 
        WHEN 1 THEN '创建中' 
        WHEN 2 THEN '运行中' 
        WHEN 3 THEN '已停止' 
        WHEN 4 THEN '已销毁' 
    END as instance_status_name
FROM user_server_instance usi
LEFT JOIN user u ON usi.user_id = u.id
WHERE usi.billing_type = 2  -- 包年包月
  AND usi.expire_time > UNIX_TIMESTAMP()  -- 未过期
  AND usi.expire_time <= UNIX_TIMESTAMP() + 7 * 86400  -- 7天内到期
  AND usi.instance_status IN (1, 2)  -- 创建中或运行中
  AND usi.is_del = 0
ORDER BY usi.expire_time ASC;

# 8. 查询按需付费实例的实时费用（当前计费周期）
SELECT 
    usi.user_id,
    usi.server_id,
    usi.server_name,
    usi.server_sku_id,
    usi.billing_cycle,
    sbm.price as unit_price,
    CASE usi.billing_cycle
        WHEN '1H' THEN CEIL((UNIX_TIMESTAMP() - usi.start_time) / 3600.0)
        WHEN '1D' THEN CEIL((UNIX_TIMESTAMP() - usi.start_time) / 86400.0)
    END as billing_units,
    CASE usi.billing_cycle
        WHEN '1H' THEN CEIL((UNIX_TIMESTAMP() - usi.start_time) / 3600.0) * sbm.price
        WHEN '1D' THEN CEIL((UNIX_TIMESTAMP() - usi.start_time) / 86400.0) * sbm.price
    END as estimated_cost,
    sbm.currency,
    FROM_UNIXTIME(usi.start_time) as start_time,
    ROUND((UNIX_TIMESTAMP() - usi.start_time) / 3600.0, 2) as running_hours
FROM user_server_instance usi
LEFT JOIN server_sku_billing_method sbm ON usi.server_sku_id = sbm.server_sku_id 
    AND usi.billing_type = sbm.billing_type 
    AND usi.billing_cycle = sbm.billing_cycle
WHERE usi.billing_type = 1  -- 按需付费
  AND usi.instance_status = 2  -- 运行中
  AND usi.end_time = 0  -- 未结束
  AND usi.is_del = 0
  AND sbm.status = 1
ORDER BY estimated_cost DESC;

# 9. 查询系统的收入统计
SELECT 
    DATE_FORMAT(FROM_UNIXTIME(br.settlement_time), '%Y-%m') as settlement_month,
    CASE br.billing_type 
        WHEN 1 THEN '按需付费' 
        WHEN 2 THEN '包年包月' 
    END as billing_type_name,
    COUNT(DISTINCT br.user_id) as user_count,
    COUNT(*) as billing_count,
    SUM(CASE WHEN br.record_type = 1 THEN br.amount ELSE 0 END) as revenue,
    SUM(CASE WHEN br.record_type = 2 THEN br.amount ELSE 0 END) as refund,
    SUM(CASE WHEN br.record_type = 1 THEN br.amount ELSE -br.amount END) as net_revenue,
    br.currency
FROM billing_record br
WHERE br.settlement_status = 2  -- 已结算
  AND br.settlement_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 12 MONTH))
  AND br.is_del = 0
GROUP BY DATE_FORMAT(FROM_UNIXTIME(br.settlement_time), '%Y-%m'), br.billing_type, br.currency
ORDER BY settlement_month DESC, br.billing_type;
