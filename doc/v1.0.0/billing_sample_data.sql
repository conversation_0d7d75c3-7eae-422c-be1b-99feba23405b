# 计费方式示例数据
# 这个文件展示了如何配置按需付费和包年包月两种计费方式

# 1. 服务器套餐规格定价表示例数据
# 假设有一个2核4G的套餐，SKU ID为 "ecs.s6-c1m2.large"

# 按需付费 - 按小时计费
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 1, 3600, 0.5000, 0.5000,
    1.0000, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G按小时计费'
);

# 按需付费 - 按天计费（通常有折扣）
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 1, 86400, 10.0000, 12.0000,
    0.8333, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G按天计费，相比小时计费有折扣'
);

# 包年包月 - 1个月
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 2, '1M', 300.0000, 360.0000, 
    0.8333, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G包月，相比按小时有折扣'
);

# 包年包月 - 3个月
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 2, '3M', 850.0000, 1080.0000, 
    0.7870, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G包3个月，相比包月有额外折扣'
);

# 包年包月 - 6个月
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 2, '6M', 1600.0000, 2160.0000, 
    0.7407, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G包半年，相比包月有更大折扣'
);

# 包年包月 - 1年
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 2, '1Y', 3000.0000, 4320.0000, 
    0.6944, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G包年，相比包月有最大折扣'
);

# 包年包月 - 2年
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 2, '2Y', 5500.0000, 8640.0000, 
    0.6366, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G包2年，长期使用最优惠'
);

# 包年包月 - 3年
INSERT INTO `server_sku_billing_method` (
    `server_sku_id`, `billing_type`, `billing_cycle`, `price`, `original_price`, 
    `discount_rate`, `currency`, `region_id`, `zone_id`, `effective_time`, 
    `expire_time`, `status`, `remark`
) VALUES (
    'ecs.s6-c1m2.large', 2, '3Y', 8000.0000, 12960.0000, 
    0.6173, 'CNY', 'default', '', UNIX_TIMESTAMP(), 
    0, 1, '2核4G包3年，超长期使用最优惠'
);

# 2. 用户订单示例数据

# 按需付费订单示例
INSERT INTO `user_order` (
    `user_id`, `order_no`, `server_sku_id`, `billing_method_id`, `billing_type`, 
    `billing_cycle`, `quantity`, `unit_price`, `total_amount`, `discount_amount`, 
    `actual_amount`, `currency`, `order_status`, `pay_status`, `pay_time`, 
    `expire_time`, `remark`
) VALUES (
    1, 'ORDER_20250817_001', 'ecs.s6-c1m2.large', 1, 1, 
    '1H', 1, 0.5000, 0.5000, 0.0000, 
    0.5000, 'CNY', 2, 2, UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP() + 3600, '按需付费订单，按小时计费'
);

# 包年包月订单示例
INSERT INTO `user_order` (
    `user_id`, `order_no`, `server_sku_id`, `billing_method_id`, `billing_type`, 
    `billing_cycle`, `quantity`, `unit_price`, `total_amount`, `discount_amount`, 
    `actual_amount`, `currency`, `order_status`, `pay_status`, `pay_time`, 
    `expire_time`, `remark`
) VALUES (
    1, 'ORDER_20250817_002', 'ecs.s6-c1m2.large', 5, 2, 
    '1Y', 1, 3000.0000, 3000.0000, 1320.0000, 
    3000.0000, 'CNY', 2, 2, UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP() + 3600, '包年包月订单，包年计费'
);

# 3. 用户服务器实例示例数据

# 按需付费实例
INSERT INTO `user_server_instance` (
    `user_id`, `order_id`, `server_id`, `server_name`, `server_sku_id`, 
    `billing_type`, `billing_cycle`, `instance_status`, `start_time`, `end_time`, 
    `expire_time`, `auto_renew`, `region_id`, `zone_id`
) VALUES (
    1, 1, 'server-001', 'web-server-01', 'ecs.s6-c1m2.large', 
    1, '1H', 2, UNIX_TIMESTAMP(), 0, 
    0, 0, 'default', ''
);

# 包年包月实例
INSERT INTO `user_server_instance` (
    `user_id`, `order_id`, `server_id`, `server_name`, `server_sku_id`, 
    `billing_type`, `billing_cycle`, `instance_status`, `start_time`, `end_time`, 
    `expire_time`, `auto_renew`, `region_id`, `zone_id`
) VALUES (
    1, 2, 'server-002', 'db-server-01', 'ecs.s6-c1m2.large', 
    2, '1Y', 2, UNIX_TIMESTAMP(), 0, 
    UNIX_TIMESTAMP() + 365*24*3600, 1, 'default', ''
);

# 4. 计费记录示例数据

# 按需付费计费记录
INSERT INTO `billing_record` (
    `user_id`, `instance_id`, `server_id`, `billing_type`, `billing_cycle`, 
    `start_time`, `end_time`, `duration`, `unit_price`, `amount`, 
    `currency`, `record_type`, `settlement_status`, `settlement_time`, `remark`
) VALUES (
    1, 1, 'server-001', 1, '1H', 
    UNIX_TIMESTAMP() - 3600, UNIX_TIMESTAMP(), 3600, 0.5000, 0.5000, 
    'CNY', 1, 2, UNIX_TIMESTAMP(), '按小时计费记录'
);

# 包年包月计费记录
INSERT INTO `billing_record` (
    `user_id`, `instance_id`, `server_id`, `billing_type`, `billing_cycle`, 
    `start_time`, `end_time`, `duration`, `unit_price`, `amount`, 
    `currency`, `record_type`, `settlement_status`, `settlement_time`, `remark`
) VALUES (
    1, 2, 'server-002', 2, '1Y', 
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 365*24*3600, 365*24*3600, 3000.0000, 3000.0000, 
    'CNY', 1, 2, UNIX_TIMESTAMP(), '包年计费记录'
);

# 5. 续费记录示例数据

# 自动续费记录
INSERT INTO `renewal_record` (
    `user_id`, `instance_id`, `order_id`, `renewal_type`, `renewal_cycle`, 
    `renewal_amount`, `original_expire_time`, `new_expire_time`, `renewal_status`, 
    `renewal_time`, `remark`
) VALUES (
    1, 2, 3, 2, '1Y', 
    3000.0000, UNIX_TIMESTAMP() + 365*24*3600, UNIX_TIMESTAMP() + 2*365*24*3600, 2, 
    UNIX_TIMESTAMP(), '自动续费1年'
);
