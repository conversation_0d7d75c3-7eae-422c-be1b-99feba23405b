spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: YUj5ePccyIoUCrUF
  redis:
    redisson:
      config: |
        singleServerConfig:
          address: "redis://home.2ops.top:32379"  
          password: "YUj5ePccyIoUCrUF"          
          timeout: 3000                     
          retryAttempts: 3                  
          retryInterval: 1500               
          subscriptionsPerConnection: 5   
          clientName: "my-redisson-client" 
          database: 0                      
    

cloudpods:
  client:
    authUrl: https://*************:30001/api/s/identity/v3
    user: admin
    password: admin@123
    project: system
    projectDomain: Default
    userDomain: Default


