spring:
  main:
    allow-circular-references: true
  profiles:
    active: @profileActive@
  application:
    name: podsail
  config:
    import:
      - optional:classpath:application-@profileActive@.yaml
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB


server:
  servlet:
    context-path: /podsail
    encoding:
      charset: UTF-8
      force-response: true
      force-request: false
  port: 8080
management:
  server:
    port: 9902
  health:
    elasticsearch:
      enabled: false
    mail:
      enabled: false
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      base-path: /
      path-mapping:
        health: k8s_readiness
        info: k8s_liveiness
        prometheus: metrics
      exposure:
        include: '*'
        exclude: metrics
  metrics:
    tags:
      application: @project.artifactId@

logging:
  config: classpath:logback-spring.xml
  level:
    root: info
    com:
      daddylab:
        msaiagent: debug
    org:
      springframework:
        ai: trace

mybatis-plus:
  type-aliases-package: com.cloudpod.podsail.db.entity
  type-enums-package: com.cloudpod.podsail.db.enums
  mapper-locations: classpath*:com/cloudpod/podsail/db/**/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

password:
  admin-salt: "$2a$16$stuPXlf0vhLiNWE/DjYHdO"
  user-salt: "$2a$16$iCXkAjxtuJ6f37V3/U6Oou"

