                                                                                                            
                                                 dddddddd                                                   
PPPPPPPPPPPPPPPPP                                d::::::d                                     iiii  lllllll 
P::::::::::::::::P                               d::::::d                                    i::::i l:::::l 
P::::::PPPPPP:::::P                              d::::::d                                     iiii  l:::::l 
PP:::::P     P:::::P                             d:::::d                                            l:::::l 
  P::::P     P:::::P   ooooooooooo       ddddddddd:::::d     ssssssssss     aaaaaaaaaaaaa   iiiiiii  l::::l 
  P::::P     P:::::P oo:::::::::::oo   dd::::::::::::::d   ss::::::::::s    a::::::::::::a  i:::::i  l::::l 
  P::::PPPPPP:::::P o:::::::::::::::o d::::::::::::::::d ss:::::::::::::s   aaaaaaaaa:::::a  i::::i  l::::l 
  P:::::::::::::PP  o:::::ooooo:::::od:::::::ddddd:::::d s::::::ssss:::::s           a::::a  i::::i  l::::l 
  P::::PPPPPPPPP    o::::o     o::::od::::::d    d:::::d  s:::::s  ssssss     aaaaaaa:::::a  i::::i  l::::l 
  P::::P            o::::o     o::::od:::::d     d:::::d    s::::::s        aa::::::::::::a  i::::i  l::::l 
  P::::P            o::::o     o::::od:::::d     d:::::d       s::::::s    a::::aaaa::::::a  i::::i  l::::l 
  P::::P            o::::o     o::::od:::::d     d:::::d ssssss   s:::::s a::::a    a:::::a  i::::i  l::::l 
PP::::::PP          o:::::ooooo:::::od::::::ddddd::::::dds:::::ssss::::::sa::::a    a:::::a i::::::il::::::l
P::::::::P          o:::::::::::::::o d:::::::::::::::::ds::::::::::::::s a:::::aaaa::::::a i::::::il::::::l
P::::::::P           oo:::::::::::oo   d:::::::::ddd::::d s:::::::::::ss   a::::::::::aa:::ai::::::il::::::l
PPPPPPPPPP             ooooooooooo      ddddddddd   ddddd  sssssssssss      aaaaaaaaaa  aaaaiiiiiiiillllllll
                                                                                                            
                                                                                                            
Server name         : ${spring.application.name}
SpringBoot Version  : ${spring-boot.version}
                                                                                                            
                                                                                                            
                                                                                                            
                                                                                                            