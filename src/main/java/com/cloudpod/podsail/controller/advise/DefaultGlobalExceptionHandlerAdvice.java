package com.cloudpod.podsail.controller.advise;

import com.cloudpod.podsail.common.base.exception.BaseErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.base.response.Response;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import org.springframework.web.util.NestedServletException;

import java.nio.file.AccessDeniedException;
import java.util.*;

@Slf4j
@RestControllerAdvice
public class DefaultGlobalExceptionHandlerAdvice {

  @ExceptionHandler(value = {NoResourceFoundException.class})
  public <T> Response<T> resourceNotFoundException(NoResourceFoundException e) {
    log.error("NoResourceFoundException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.RESOURCE_NOT_FOUND, e.getMessage());
  }

  @ExceptionHandler(value = {HttpMediaTypeException.class})
  public <E> Response<E> httpMediaTypeException(Exception e) {
    log.error("HttpMediaTypeException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.RESOURCE_NOT_FOUND, e.getMessage());
  }

  @ExceptionHandler(value = {HttpRequestMethodNotSupportedException.class})
  public <E> Response<E> httpRequestMethodNotSupportedException(Exception e) {
    log.error("HttpRequestMethodNotSupportedException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.RESOURCE_NOT_FOUND, e.getMessage());
  }

  @ExceptionHandler(value = {MissingServletRequestParameterException.class})
  public <E> Response<E> missingServletRequestParameterException(
      MissingServletRequestParameterException e) {
    log.error("MissingServletRequestParameterException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.ARGUMENT_NOT_VALID);
  }

  @ExceptionHandler(value = {MultipartException.class})
  public <E> Response<E> uploadFileLimitException(MultipartException e) {
    log.error("MultipartException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.UPLOAD_FILE_SIZE_LIMIT);
  }

  @ExceptionHandler(value = {MethodArgumentNotValidException.class})
  public Response<String> serviceException(MethodArgumentNotValidException e) {
    log.error("MethodArgumentNotValidException msg:{}", e.getMessage(), e);
    return Response.fail(
        BaseErrorCodeEnum.ARGUMENT_NOT_VALID,
        Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage());
  }

  @ExceptionHandler(value = {NestedServletException.class})
  public Response<String> serviceException(NestedServletException e) {
    log.error("AssertionError msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.ARGUMENT_NOT_VALID, e.getMessage());
  }

  @ExceptionHandler(value = {MethodArgumentTypeMismatchException.class})
  public Response<String> methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
    log.error("MethodArgumentTypeMismatchException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.ARGUMENT_NOT_VALID, e.getMessage());
  }

  @ExceptionHandler(value = {ConstraintViolationException.class})
  public Response<String> constraintViolationException(ConstraintViolationException e) {
    log.error("ConstraintViolationException msg:{}", e.getMessage(), e);
    Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
    Iterator<ConstraintViolation<?>> iterator = constraintViolations.iterator();
    List<String> msgList = new ArrayList<>();
    while (iterator.hasNext()) {
      ConstraintViolation<?> cvl = iterator.next();
      msgList.add(cvl.getMessageTemplate());
    }
    return Response.fail(BaseErrorCodeEnum.ARGUMENT_NOT_VALID, String.join(",", msgList));
  }

  @ExceptionHandler(value = {DuplicateKeyException.class})
  public <E> Response<E> duplicateKeyException(DuplicateKeyException e) {
    log.error("DuplicateKeyException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.DUPLICATE_PRIMARY_KEY);
  }

  @ExceptionHandler(value = {PodSailException.class})
  public <E> Response<E> baseException(PodSailException e) {
    log.error(" PodSailException msg:{} code:{}", e.getMessage(), e.getCode(), e);
    return Response.fail(e);
  }

  //    @ExceptionHandler(value = {HystrixRuntimeException.class})
  //    public Response<String> baseException(HystrixRuntimeException e) {
  //        log.error("HystrixRuntimeException msg:{}", e.getMessage());
  //        return Response.fail("调用三方服务异常");
  //    }

  @ExceptionHandler(value = {UnexpectedRollbackException.class})
  public <E> Response<E> unexpectedRollback(UnexpectedRollbackException e) {
    log.error("UnexpectedRollbackException msg:{}", e.getMessage());
    return Response.fail(BaseErrorCodeEnum.BUSINESS_OPERATE_FAILED_PLEASE_TRY_AGAIN);
  }

  @ExceptionHandler(AccessDeniedException.class)
  public <E> Response<E> handleAuthorizationException(AccessDeniedException e) {
    log.error("AccessDeniedException msg:{}", e.getMessage(), e);
    return Response.fail(BaseErrorCodeEnum.USER_NOT_LOGIN);
  }

  /** 自定义验证异常 */
  @ExceptionHandler({BindException.class})
  public <E> Response<E> validatedBindException(BindException e) {
    //        log.error("BindException msg:{}", e.getMessage(), e);
    String message = e.getAllErrors().get(0).getDefaultMessage();
    log.info("BindException msg:{}", message);
    return Response.fail(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, message);
  }

  /** 自定义验证异常 */
  @ExceptionHandler({IllegalArgumentException.class})
  public <E> Response<E> illegalArgument(IllegalArgumentException e) {
    log.error("IllegalArgumentException msg:{}", e.getMessage(), e);
    String message = e.getMessage();
    log.info("IllegalArgumentException msg:{}", message);
    return Response.fail(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, e.getMessage());
  }

  @ExceptionHandler({HttpMessageNotReadableException.class})
  public <E> Response<E> validatedHeepMessageException(HttpMessageNotReadableException e) {
    log.error("HttpMessageNotReadableException msg:{}", e.getMessage(), e);
    Throwable exception = e;
    while (exception.getCause() != null) {
      exception = exception.getCause();
    }
    if (exception instanceof PodSailException) {
      return Response.fail(
          PodSailErrorCodeEnum.REQUEST_PARAM_ERROR,
          ((PodSailException) e.getCause().getCause()).getMsg());
    }
    return Response.fail(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "参数格式不正确");
  }

  @ExceptionHandler(value = {Exception.class})
  public <E> Response<E> exception(Exception e) {
    log.error("Exception msg:{}", e.getMessage(), e);
    sendAlert(e);
    return Response.fail(BaseErrorCodeEnum.SYSTEM_ERROR);
  }

  @ExceptionHandler(value = {Throwable.class})
  @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
  public <E> Response<E> throwable(Throwable e) {
    sendAlert(e);
    return Response.fail();
  }

  /**
   * 全局异常发送预警
   *
   * @param e
   */
  void sendAlert(Throwable e) {
    if (null != e.getMessage() && e.getMessage().contains("Broken pipe")) {
      return;
    }
    log.error("[Throwable]", e);
  }
}
