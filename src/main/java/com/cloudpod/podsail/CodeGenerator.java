package com.cloudpod.podsail;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.lang.ConsoleTable;
import cn.hutool.core.text.NamingCase;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.nio.file.Paths;
import java.util.*;

public class CodeGenerator {

  public static void main(String[] args) {
    final String[][] datasourceConfigList = {
      new String[] {
        "podsail", // scheme name
        "********************************************************************************************", // jdbc url
        "root", // username
        "YUj5ePccyIoUCrUF" // password
      },
    };
    Console.print("please select database:\n");
    final ConsoleTable consoleTable = ConsoleTable.create().setSBCMode(false);
    consoleTable.addHeader("index", "scheme", "jdbc url", "username");
    for (int i = 0; i < datasourceConfigList.length; i++) {
      final String[] datasourceConfig = datasourceConfigList[i];
      consoleTable.addBody(
          String.valueOf(i), datasourceConfig[0], datasourceConfig[1], datasourceConfig[2]);
    }
    Console.table(consoleTable);
    Console.print("please input database index: (default 0)\n");
    final int selectIndex =
        Optional.ofNullable(Console.input())
            .filter(StrUtil::isNotBlank)
            .map(Integer::parseInt)
            .orElse(0);
    final String[] datasourceConfig = datasourceConfigList[selectIndex];

    Console.print("please input tables: \n");
    final List<String> tables = getTables(Console.input());
    if (CollectionUtil.isEmpty(tables)) {
      Console.error("bye");
      return;
    }

    final String schema = NamingCase.toCamelCase(datasourceConfig[0], '_');
    FastAutoGenerator.create(datasourceConfig[1], datasourceConfig[2], datasourceConfig[3])
        .globalConfig(
            builder ->
                builder
                    .author("CodeGenerator")
                    .outputDir(
                        Paths.get(System.getProperty("user.dir")) + "/src/main/java/")
                    .commentDate("yyyy-MM-dd")
                    .disableOpenDir())
        .packageConfig(
            builder ->
                builder
                    .parent("com.cloudpod.podsail.db")
                    .entity("entity")
                    .mapper("mapper")
                    .service("dao")
                    .serviceImpl("dao.impl")
                    .xml("mapper.xml"))
        .strategyConfig(
            builder ->
                builder
                    .addInclude(tables)
                    .entityBuilder()
                    .javaTemplate("templates/entity.java")
                    .enableFileOverride()
                    .enableChainModel()
                    .enableTableFieldAnnotation()
                    .enableLombok()
                    .superClass("com.cloudpod.podsail.db.base.Entity")
                    .enableActiveRecord()
                    .addIgnoreColumns(
                        "id",
                        "created_at",
                        "created_uid",
                        "updated_at",
                        "updated_uid",
                        "deleted_at",
                        "is_del")
                    .controllerBuilder()
                    .disable()
                    .mapperBuilder()
                    .mapperTemplate("templates/mapper.java")
                    .serviceBuilder()
                    .convertServiceFileName((entityName -> entityName + "Dao"))
                    .convertServiceImplFileName((entityName -> entityName + "DaoImpl"))
                    .serviceImplTemplate("templates/serviceImpl.java"))
        .templateEngine(new FreemarkerTemplateEngine())
        .injectionConfig(
            builder -> {
              Map<String, Object> map = new HashMap<>(2);
              map.put("dsIndex", selectIndex);
              map.put("dsValue", datasourceConfig[0]);
              builder.customMap(map);
            })
        .execute();
  }

  // 处理 all 情况
  protected static List<String> getTables(String tables) {
    return "all".equals(tables)
        ? Collections.singletonList(".*")
        : Arrays.asList(tables.split(","));
  }
}
