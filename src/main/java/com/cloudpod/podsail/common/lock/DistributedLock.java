package com.cloudpod.podsail.common.lock;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface DistributedLock {
    /**
     * Lock Key, 默认是方法名
     */
    String value() default "";

    /**
     * 拼接键名的时候如何处理方法参数
     *
     * @see SearchKeyStrategy
     * @see DistributedLockKey
     */
    SearchKeyStrategy searchKey() default SearchKeyStrategy.NONE;

    /**
     * 获取锁超时失败的错误提示信息
     */
    String msg() default "服务繁忙，请稍后再试";

    /**
     * 在加锁失败时返回null而非抛出异常
     */
    boolean nullValueOnLockFail() default false;

    /**
     * 获取锁的最大等待时长（秒），设置为0表示不等待
     */
    long waitTime() default 30;

    /**
     * 持有锁的最长时间，超过自动释放
     */
    long leaseTime() default 60;

    /**
     * 拼接键名的时候如何处理方法参数
     */
    enum SearchKeyStrategy {
        /**
         * 无需拼接参数
         */
        NONE,
        /**
         * 拼接带@DistributedLockKey注解的参数，找到一个后停止
         */
        PARAMETER,
        /**
         * 拼接带@DistributedLockKey注解的参数（支持多个参数）
         */
        MULTI_PARAMETER,
        /**
         * 在方法参数类中搜索带@DistributedLockKey注解的属性，找到一个后停止
         */
        PARAMETER_PROPERTY,
        /**
         * 在方法参数类中搜索带@DistributedLockKey注解的属性（支持多个参数）
         */
        MULTI_PARAMETER_PROPERTY,
    }
}
