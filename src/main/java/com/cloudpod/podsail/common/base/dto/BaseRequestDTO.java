package com.cloudpod.podsail.common.base.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/9/11
 */
@Data
@Accessors(chain = true)
public class BaseRequestDTO implements DTO {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty(
      value = "当前用户ID",
      hidden = true,
      accessMode = ApiModelProperty.AccessMode.READ_ONLY)
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private Long currentUserId = 0L;
}
