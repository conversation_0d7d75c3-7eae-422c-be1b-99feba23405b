package com.cloudpod.podsail.common.base.dto;

import com.fasterxml.jackson.annotation.JsonIncludeProperties;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2024/9/14
 */
@Data
@JsonIncludeProperties({"timeStart", "timeEnd"})
public class TimestampRange implements DTO {
  @ApiModelProperty("开始时间")
  @NotNull
  private Long timeStart;

  @ApiModelProperty("结束时间")
  @NotNull
  private Long timeEnd;
}
