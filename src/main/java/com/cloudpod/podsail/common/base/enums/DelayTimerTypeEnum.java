package com.cloudpod.podsail.common.base.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum DelayTimerTypeEnum implements IEnum<String> {
    /**
     * style
     */
    TEST_DELAY_TIMER("TEST_DELAY_TIMER", "测试", "测试"),

    ;

    private String value;
    private String name;
    private String desc;

    DelayTimerTypeEnum(String value, String name, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

    public static DelayTimerTypeEnum getEnumByValue(String value) {
        if (Objects.nonNull(value)) {
            for (DelayTimerTypeEnum e : DelayTimerTypeEnum.values()) {
                if (Objects.equals(e.value, value)) {
                    return e;
                }
            }
        }
        return null;
    }
}
