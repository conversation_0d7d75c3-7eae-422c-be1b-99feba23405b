package com.cloudpod.podsail.common.base.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @className BasePageQuery
 * <AUTHOR>
 * @date 2024/9/3 11:36
 * @description: TODO 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class BasePageQuery extends BaseRequestDTO {

    @ApiModelProperty("当前页码")
    private Long current = 1L;

    @ApiModelProperty("每页记录数")
    private Long size = 20L;

    @ApiModelProperty(hidden = true)
    public <T extends IPage<R>, R> T page() {
        return page(true);
    }

    @ApiModelProperty(hidden = true)
    @SuppressWarnings({"unchecked"})
    public <T extends IPage<R>, R> T page(Boolean isSearchCount) {
        return (T) new Page<R>(getCurrent(), getSize(), isSearchCount);
    }
}
