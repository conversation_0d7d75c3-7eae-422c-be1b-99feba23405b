package com.cloudpod.podsail.common.base.exception;

/**
 * 错误码
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/4/12 10:10 上午
 */
public interface ErrorCode {
    /**
     * 错误类型
     *
     * @return ErrorTypeEnumR
     */
    ErrorTypeEnum getType();

    /**
     * 错误码
     *
     * @return int
     */
    int getCode();

    /**
     * 错误信息
     *
     * @return String
     */
    String getMsg();
}
