package com.cloudpod.podsail.common.base.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.ErrorCode;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用返回对象
 * @param <T>
 */
@Getter
public class Response<T> {

    public static final int SUCCESSFUL_CODE = 0;

    public static final String SUCCESSFUL_MSG = "success";

    /**
     * 状态码
     */
    private Integer code;

    private Boolean flag;

    /**
     * 描述信息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    public Response() {

    }

    /**
     * @param errorType 错误类型
     */
    public Response(ErrorCode errorType) {
        this.code = errorType.getCode();
        this.msg = errorType.getMsg();
        this.flag = false;
    }

    /**
     * @param  PodSailException
     */
    public Response(PodSailException PodSailException) {
        this.code = PodSailException.getCode();
        this.msg = PodSailException.getMsg();
        this.flag = false;
    }

    /**
     * @param errorType 错误类型
     * @param data 返回数据
     */
    public Response(ErrorCode errorType, T data) {
        this.msg = errorType.getMsg();
        this.code = errorType.getCode();
        this.data = data;
        this.flag = false;
    }

    /**
     * @param errorType 错误类型
     * @param msg 描述信息
     */
    public Response(ErrorCode errorType, String msg) {
        this.msg = errorType.getMsg();
        this.code = errorType.getCode();
        this.msg = msg;
        this.flag = false;
    }

    /**
     * 内部使用，用于构造成功的结果
     *
     * @param code 状态码
     * @param msg  描述信息
     * @param data 返回数据
     */
    private Response(int code, String msg, T data, boolean flag) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.flag = flag;
    }
    /**
     * 内部使用，用于构造成功的结果
     *
     * @param code 状态码
     * @param msg  描述信息
     * @param data 返回数据
     */
    private Response(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.flag = true;
    }

    /**
     * 成功并返回结果数据
     *
     * @param data 返回数据
     * @return Result
     */
    public static <E> Response<E> success(E data) {
        return new Response<>(SUCCESSFUL_CODE, SUCCESSFUL_MSG, data);
    }

    /**
     * 成功
     *
     * @return Result
     */
    public static <E> Response<E> success() {
        return success(null);
    }

    /**
     * 系统异常没有返回数据
     *
     * @return Result
     */
    public static <E> Response<E> fail() {
        return new Response<>(PodSailErrorCodeEnum.SYSTEM_ERROR);
    }

    /**
     * 系统异常没有返回数据
     *
     * @param techErrorCodeEnum 系统异常
     * @return Result
     */
    public static <E> Response<E> fail(PodSailErrorCodeEnum techErrorCodeEnum) {
        return fail((ErrorCode) techErrorCodeEnum, null);
    }

    /**
     * 系统异常
     *
     * @param techErrorCodeEnum 系统异常
     * @return Result
     */
    public static <E> Response<E> fail(PodSailErrorCodeEnum techErrorCodeEnum, String msg) {
        return fail((ErrorCode) techErrorCodeEnum, msg);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param data 返回数据
     * @return Result
     */
    public static <E> Response<E> fail(PodSailErrorCodeEnum techErrorCodeEnum, E data) {
        return new Response<>(techErrorCodeEnum.getCode(), techErrorCodeEnum.getMsg(), data);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param errorType 错误类型
     * @param data      返回数据
     * @return Result
     */
    public static <E> Response<E> fail(ErrorCode errorType, E data) {
        return new Response<>(errorType, data);
    }

    /**
     * 系统异常类并返回自定义描述信息
     *
     * @param errorType 错误类型
     * @param msg      描述信息
     * @return Result
     */
    public static <E> Response<E> fail(ErrorCode errorType, String msg) {
        return new Response<>(errorType, msg);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param errorType 错误类型
     * @return Result
     */
    public static <E> Response<E> fail(ErrorCode errorType) {
        return new Response<>(errorType);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param data  返回数据
     * @return Result
     */
    public static <E> Response<E> fail(E data) {
        return new Response<>(PodSailErrorCodeEnum.SYSTEM_ERROR, data);
    }
    /**
     * 系统异常类并返回结果数据
     *
     * @param PodSailException  返回数据
     * @return Result
     */
    public static <E> Response<E> fail(PodSailException PodSailException) {
        return new Response<>(PodSailException);
    }

    public static <T> Response<IPage<T>> emptyPage(Long c, Long s) {
        IPage<T> pp = new Page<>(c, s, 0);
        pp.setRecords(new ArrayList<>());
        return Response.success(pp);
    }

    public static <T> Response<IPage<T>> resultPage(Long c, Long s, List<T> resList, Long t) {
        IPage<T> pp = new Page<>(c, s, t);
        pp.setRecords(resList);
        return Response.success(pp);
    }
}
