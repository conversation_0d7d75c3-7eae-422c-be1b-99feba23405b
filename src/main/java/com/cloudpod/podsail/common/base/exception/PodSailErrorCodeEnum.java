package com.cloudpod.podsail.common.base.exception;

import lombok.Getter;

/**
 * open模块错误枚举 @Auther: <EMAIL>
 *
 * @version: 1.0.0 @Date: 2021/04/16/4:01 下午
 */
@Getter
public enum PodSailErrorCodeEnum implements ErrorCode {
  SYSTEM_ERROR(ErrorTypeEnum.SYSTEM_ERROR, 500, "系统异常"),

  // 参数异常
  REQUEST_PARAM_ERROR(ErrorTypeEnum.REQUEST_ERROR, 400, "请求参数异常"),
  REQUEST_PARAM_MISS(ErrorTypeEnum.REQUEST_ERROR, 401, "请求参数缺失"),
  REQUEST_PARAM_FORMAT_ERROR(ErrorTypeEnum.REQUEST_ERROR, 402, "请求参数格式错误"),

  // 登录/权限异常
  REQUEST_FORBIDDEN_ERROR(ErrorTypeEnum.SECURITY_ERROR, 403, "无权限"),
  TOKEN_TIMEOUT(ErrorTypeEnum.SECURITY_ERROR, 406, "登陆信息已过期，请重新登录"),
  TOKEN_NOT_FOUND(ErrorTypeEnum.SECURITY_ERROR, 406, "Token未传"),
  TOKEN_VALID_FAILED(ErrorTypeEnum.SECURITY_ERROR, 406, "Token验证不通过"),
  SESSION_LOAD_ERROR(ErrorTypeEnum.SECURITY_ERROR, 407, "用户信息加载异常"),

  // 资源错误
  ADD_RESOURCE_ERROR(ErrorTypeEnum.BIZ_ERROR, 100100, "添加资源失败"),
  DELETE_RESOURCE_ERROR(ErrorTypeEnum.BIZ_ERROR, 100101, "删除资源失败"),
  UPDATE_RESOURCE_ERROR(ErrorTypeEnum.BIZ_ERROR, 100102, "更新资源失败"),
  RESOURCE_NOT_FOUND(ErrorTypeEnum.BIZ_ERROR, 100103, "未找到记录"),
  OPERATOR_FAIL(ErrorTypeEnum.BIZ_ERROR, 100104, "操作失败"),
  BUSINESS_ERROR(ErrorTypeEnum.BIZ_ERROR, 100105, "业务异常"),
  EXPORT_ERROR(ErrorTypeEnum.BIZ_ERROR, 100106, "导出异常"),
  DELETE_RESOURCE_REJECT(ErrorTypeEnum.BIZ_ERROR, 100107, "不允许删除资源"),
  THIRD_SERVER_ERROR(ErrorTypeEnum.BIZ_ERROR, 100108, "调用第三方服务异常"),


  ;
  private final ErrorTypeEnum type;
  private final int code;
  private final String msg;

  PodSailErrorCodeEnum(ErrorTypeEnum type, int code, String msg) {
    this.type = type;
    this.code = code;
    this.msg = msg;
  }
}
