package com.cloudpod.podsail.common.base.dto;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @since 2024/9/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class RequestByID<T> extends BaseRequestDTO {
  @ApiModelProperty(value = "ID")
  @NotNull
  private T id;
}
