package com.cloudpod.podsail.common.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年10月28日 2:55 PM
 */
@Data
public class SimpleStandardCopyDTO {

    @ApiModelProperty("标准号")
    private String no;

    @ApiModelProperty("标准名称")
    private String name;

    @ApiModelProperty("标准状态 0-待提交 1-即将实施 2-现行 3-废止")
    private Integer status;

    @ApiModelProperty("标准实施时间")
    private String issueTime;

    /**
     * 标准公告-相关标准状态 其中标准状态无法和上面[status]做匹配映射，单独做个副本。
     */
    @ApiModelProperty("标准状态字符串（标准公告-相关标准状态）")
    private String statusCopy;
}
