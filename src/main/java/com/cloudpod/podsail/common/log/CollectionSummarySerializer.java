package com.cloudpod.podsail.common.log;

import java.util.Collection;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
public class CollectionSummarySerializer implements Function<Collection<?>, String> {
  private final int collectionLimit;
  private final Function<Object, String> valueSerializer;

  public CollectionSummarySerializer(int collectionLimit, Function<Object, String> valueSerializer) {
    this.collectionLimit = collectionLimit;
    this.valueSerializer = valueSerializer;
  }

  @Override
  public String apply(Collection<?> value) {
    final int size = value.size();
    if (size <= collectionLimit) {
      return "[" + value.stream().map(valueSerializer).collect(Collectors.joining(",")) + "]";
    } else {
      final StringBuilder serializeStr = new StringBuilder().append("[");
      final int truncateLeft = collectionLimit / 2;
      final int truncateRight = collectionLimit - truncateLeft;
      int i = 0;
      for (Object item : value) {
        if (i < truncateLeft) {
          if (i != 0) {
            serializeStr.append(", ");
          }
          serializeStr.append(valueSerializer.apply(item));
        } else {
          break;
        }
        i++;
      }
      serializeStr.append(", \"...").append("(").append(size - collectionLimit).append(" more items)\"");
      i = 0;
      for (Object item : value) {
        if (i >= size - truncateRight) {
          serializeStr.append(", ");
          serializeStr.append(valueSerializer.apply(item));
        }
        i++;
      }
      return serializeStr.toString();
    }
  }
}
