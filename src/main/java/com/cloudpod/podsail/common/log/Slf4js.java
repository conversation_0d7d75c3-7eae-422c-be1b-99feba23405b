package com.cloudpod.podsail.common.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;

import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.LongStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
public class Slf4js {

  public static void trace(Logger logger, String pattern, Object... vars) {
    log(logger, Level.TRACE, pattern, vars);
  }

  public static void debug(Logger logger, String pattern, Object... vars) {
    log(logger, Level.DEBUG, pattern, vars);
  }

  public static void info(Logger logger, String pattern, Object... vars) {
    log(logger, Level.INFO, pattern, vars);
  }

  public static void warn(Logger logger, String pattern, Object... vars) {
    log(logger, Level.WARN, pattern, vars);
  }

  public static void error(Logger logger, String pattern, Object... vars) {
    log(logger, Level.ERROR, pattern, vars);
  }

  public static void log(Logger logger, Level level, String pattern, Object... vars) {
    final StringBuilder patternBuild = new StringBuilder(pattern);
    final ArrayList<Object> sortVars = new ArrayList<>(vars.length);
    Stream.of(vars)
        .filter(var -> !(var instanceof LogVar) && !(var instanceof Throwable))
        .forEach(sortVars::add);
    Stream.of(vars)
        .filter(var -> var instanceof LogVar)
        .peek(v -> patternBuild.append(", ").append("{}"))
        .forEach(sortVars::add);
    Stream.of(vars).filter(var -> var instanceof Throwable).forEach(sortVars::add);
    pattern = patternBuild.toString();
    final Object[] objects = sortVars.toArray();
    switch (level) {
      case TRACE:
        logger.trace(pattern, objects);
        break;
      case DEBUG:
        logger.debug(pattern, objects);
        break;
      case INFO:
        logger.info(pattern, objects);
        break;
      case WARN:
        logger.warn(pattern, objects);
        break;
      case ERROR:
        logger.error(pattern, objects);
        break;
      default:
        throw new IllegalArgumentException("invalid level");
    }
  }

  public static void main(String[] args) {
    final Logger logger = LoggerFactory.getLogger(Slf4js.class);
    Slf4js.info(
        logger, "test1, args:{}", LogVar.named("a", "main:83"), args, LogVar.named("b", "main:85"));
    Slf4js.error(
        logger,
        "test2",
        LogVar.named("id1", IntStream.range(0, 100).toArray()),
        LogVar.named(
            "id2", LongStream.rangeClosed(99999, 999999).boxed().collect(Collectors.toList())));
  }
}
