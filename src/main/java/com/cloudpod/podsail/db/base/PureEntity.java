package com.cloudpod.podsail.db.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(
    of = {"id"},
    callSuper = false)
public class PureEntity<T extends Model<?>> extends Model<T> implements Serializable {
  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  protected Long id;

  /** 创建时间 */
  @TableField(value = "created_at", fill = FieldFill.INSERT)
  protected Long createdAt;


  /** 更新时间 */
  @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
  protected Long updatedAt;

  /** 删除时间 */
  @TableField(value = "deleted_at")
  private Long deletedAt;

}
