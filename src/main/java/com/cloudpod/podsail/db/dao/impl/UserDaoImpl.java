package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.dao.UserDao;
import com.cloudpod.podsail.db.entity.User;
import com.cloudpod.podsail.db.mapper.UserMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class UserDaoImpl extends ServiceImpl<UserMapper, User> implements UserDao {

}
