package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.dao.RenewalRecordDao;
import com.cloudpod.podsail.db.entity.RenewalRecord;
import com.cloudpod.podsail.db.mapper.RenewalRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 续费记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class RenewalRecordDaoImpl extends ServiceImpl<RenewalRecordMapper, RenewalRecord> implements RenewalRecordDao {

}
