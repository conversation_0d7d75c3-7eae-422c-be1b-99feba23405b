package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.dao.UserServerInstanceDao;
import com.cloudpod.podsail.db.entity.UserServerInstance;
import com.cloudpod.podsail.db.mapper.UserServerInstanceMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户服务器实例表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class UserServerInstanceDaoImpl extends ServiceImpl<UserServerInstanceMapper, UserServerInstance> implements UserServerInstanceDao {

}
