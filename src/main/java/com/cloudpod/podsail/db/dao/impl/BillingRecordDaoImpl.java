package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.dao.BillingRecordDao;
import com.cloudpod.podsail.db.entity.BillingRecord;
import com.cloudpod.podsail.db.mapper.BillingRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 计费记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class BillingRecordDaoImpl extends ServiceImpl<BillingRecordMapper, BillingRecord> implements BillingRecordDao {

}
