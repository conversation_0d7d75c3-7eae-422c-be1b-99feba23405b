package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.entity.AdminUser;
import com.cloudpod.podsail.db.mapper.AdminUserMapper;
import com.cloudpod.podsail.db.dao.AdminUserDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 后台用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class AdminUserDaoImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserDao {

}
