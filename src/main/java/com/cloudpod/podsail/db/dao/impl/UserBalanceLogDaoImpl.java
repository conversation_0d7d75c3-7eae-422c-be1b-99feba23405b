package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.entity.UserBalanceLog;
import com.cloudpod.podsail.db.mapper.UserBalanceLogMapper;
import com.cloudpod.podsail.db.dao.UserBalanceLogDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户余额流水 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class UserBalanceLogDaoImpl extends ServiceImpl<UserBalanceLogMapper, UserBalanceLog> implements UserBalanceLogDao {

}
