package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.entity.PaymentTrade;
import com.cloudpod.podsail.db.mapper.PaymentTradeMapper;
import com.cloudpod.podsail.db.dao.PaymentTradeDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 支付交易表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class PaymentTradeDaoImpl extends ServiceImpl<PaymentTradeMapper, PaymentTrade> implements PaymentTradeDao {

}
