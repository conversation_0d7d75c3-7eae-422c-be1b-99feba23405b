package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.entity.UserApiKey;
import com.cloudpod.podsail.db.mapper.UserApiKeyMapper;
import com.cloudpod.podsail.db.dao.UserApiKeyDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户秘钥管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class UserApiKeyDaoImpl extends ServiceImpl<UserApiKeyMapper, UserApiKey> implements UserApiKeyDao {

}
