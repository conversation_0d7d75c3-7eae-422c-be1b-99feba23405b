package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.entity.UserOrder;
import com.cloudpod.podsail.db.mapper.UserOrderMapper;
import com.cloudpod.podsail.db.dao.UserOrderDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class UserOrderDaoImpl extends ServiceImpl<UserOrderMapper, UserOrder> implements UserOrderDao {

}
