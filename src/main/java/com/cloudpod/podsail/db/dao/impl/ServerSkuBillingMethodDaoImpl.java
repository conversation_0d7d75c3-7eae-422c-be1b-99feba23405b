package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.entity.ServerSkuBillingMethod;
import com.cloudpod.podsail.db.mapper.ServerSkuBillingMethodMapper;
import com.cloudpod.podsail.db.dao.ServerSkuBillingMethodDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 主机套餐规格定价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class ServerSkuBillingMethodDaoImpl extends ServiceImpl<ServerSkuBillingMethodMapper, ServerSkuBillingMethod> implements ServerSkuBillingMethodDao {

}
