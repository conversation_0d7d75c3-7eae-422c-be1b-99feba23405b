package com.cloudpod.podsail.db.dao.impl;

import com.cloudpod.podsail.db.entity.RefundOrder;
import com.cloudpod.podsail.db.mapper.RefundOrderMapper;
import com.cloudpod.podsail.db.dao.RefundOrderDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 退款订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class RefundOrderDaoImpl extends ServiceImpl<RefundOrderMapper, RefundOrder> implements RefundOrderDao {

}
