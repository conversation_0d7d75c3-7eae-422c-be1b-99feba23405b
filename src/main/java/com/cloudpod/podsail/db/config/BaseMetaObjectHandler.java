package com.cloudpod.podsail.db.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Objects;
import java.util.function.Supplier;

public abstract class BaseMetaObjectHandler implements MetaObjectHandler {
    /**
     * 如果字段存在，且给定值不为null，则对源对象进行填充（与strictUpdateFill相比，该方法无论源对象中该字段是否已经有值，都会强制进行填充）
     */
    <T, E extends T> MetaObjectHandler forceUpdateFill(MetaObject metaObject, String fieldName, Supplier<E> fieldVal, Class<T> fieldType) {
        findTableInfo(metaObject)
                .getFieldList().stream()
                .filter(j -> j.getProperty().equals(fieldName) && fieldType.equals(j.getPropertyType())
                        && j.isWithUpdateFill()).findFirst()
                .ifPresent(j -> forceFillStrategy(metaObject, fieldName, fieldVal));
        return this;
    }

    MetaObjectHandler forceFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        Object obj = fieldVal.get();
        if (Objects.nonNull(obj)) {
            metaObject.setValue(fieldName, obj);
        }
        return this;
    }
}
