package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 退款订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("refund_order")
public class RefundOrder extends Entity<RefundOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 关联的原始订单ID (user_order.id)
     */
    @TableField("original_order_id")
    private Long originalOrderId;

    /**
     * 退款的服务器实例ID (user_server_instance.id)
     */
    @TableField("instance_id")
    private Long instanceId;

    /**
     * 平台生成的唯一退款单号
     */
    @TableField("refund_no")
    private String refundNo;

    /**
     * 第三方支付平台的退款流水号
     */
    @TableField("out_refund_no")
    private String outRefundNo;

    /**
     * 退款原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 原订单金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 实际退款金额 (可能涉及按比例退款)
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款渠道 ORIGINAL-原路退回 BALANCE-退至余额
     */
    @TableField("refund_channel")
    private String refundChannel;

    /**
     * 退款状态 1-待审核 2-审核通过(退款中) 3-退款成功 4-退款失败 5-审核驳回
     */
    @TableField("refund_status")
    private Boolean refundStatus;

    /**
     * 退款成功时间
     */
    @TableField("success_time")
    private Long successTime;

    /**
     * 备注(如驳回原因)
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
