package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 主机套餐规格定价表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("server_sku_billing_method")
public class ServerSkuBillingMethod extends Entity<ServerSkuBillingMethod> {

    private static final long serialVersionUID = 1L;

    /**
     * 服务器套餐SKU ID
     */
    @TableField("server_sku_id")
    private String serverSkuId;

    /**
     * 计费类型 1-按需付费(postpaid) 2-包年包月(prepaid)
     */
    @TableField("billing_type")
    private Boolean billingType;

    /**
     * 计费周期 按需付费:1H,1D 包年包月:1M,3M,6M,1Y,2Y,3Y
     */
    @TableField("billing_cycle")
    private String billingCycle;

    /**
     * 价格(元)
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 原价(元)
     */
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 折扣率 0.8表示8折
     */
    @TableField("discount_rate")
    private BigDecimal discountRate;

    /**
     * 货币单位 CNY-人民币 USD-美元
     */
    @TableField("currency")
    private String currency;

    /**
     * 区域ID
     */
    @TableField("region_id")
    private String regionId;

    /**
     * 可用区ID
     */
    @TableField("zone_id")
    private String zoneId;

    /**
     * 生效时间
     */
    @TableField("effective_time")
    private Long effectiveTime;

    /**
     * 失效时间 0表示永久有效
     */
    @TableField("expire_time")
    private Long expireTime;

    /**
     * 状态 1-正常 2-已失效
     */
    @TableField("status")
    private Boolean status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
