package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 支付交易表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("payment_trade")
public class PaymentTrade extends Entity<PaymentTrade> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 平台生成的唯一交易号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 第三方支付平台的交易流水号
     */
    @TableField("out_trade_no")
    private String outTradeNo;

    /**
     * 支付渠道 ALIPAY-支付宝 WECHAT_PAY-微信支付 MANUAL-手动打款
     */
    @TableField("payment_channel")
    private String paymentChannel;

    /**
     * 交易金额
     */
    @TableField("trade_amount")
    private BigDecimal tradeAmount;

    /**
     * 交易状态 1-待支付 2-支付成功 3-支付失败 4-交易关闭
     */
    @TableField("trade_status")
    private Boolean tradeStatus;

    /**
     * 支付成功通知时间
     */
    @TableField("notify_time")
    private Long notifyTime;

    /**
     * 第三方回调的原始报文 (用于审计和排错)
     */
    @TableField("notify_content")
    private String notifyContent;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
