package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 用户订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("user_order")
public class UserOrder extends Entity<UserOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 服务器套餐SKU ID
     */
    @TableField("server_sku_id")
    private String serverSkuId;

    /**
     * 计费方式ID
     */
    @TableField("billing_method_id")
    private Long billingMethodId;

    /**
     * 计费类型 1-按需付费 2-包年包月
     */
    @TableField("billing_type")
    private Boolean billingType;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private String billingCycle;

    /**
     * 购买数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 单价
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 实际支付金额
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 货币单位
     */
    @TableField("currency")
    private String currency;

    /**
     * 订单状态 1-待支付 2-已支付 3-已取消 4-已退款
     */
    @TableField("order_status")
    private Boolean orderStatus;

    /**
     * 支付状态 1-未支付 2-已支付 3-支付失败
     */
    @TableField("pay_status")
    private Boolean payStatus;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private Long payTime;

    /**
     * 订单过期时间
     */
    @TableField("expire_time")
    private Long expireTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
