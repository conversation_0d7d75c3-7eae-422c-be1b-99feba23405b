package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 续费记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("renewal_record")
public class RenewalRecord extends Entity<RenewalRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 服务器实例ID
     */
    @TableField("instance_id")
    private Long instanceId;

    /**
     * 续费订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 续费类型 1-手动续费 2-自动续费
     */
    @TableField("renewal_type")
    private Boolean renewalType;

    /**
     * 续费周期
     */
    @TableField("renewal_cycle")
    private String renewalCycle;

    /**
     * 续费金额
     */
    @TableField("renewal_amount")
    private BigDecimal renewalAmount;

    /**
     * 原到期时间
     */
    @TableField("original_expire_time")
    private Long originalExpireTime;

    /**
     * 新到期时间
     */
    @TableField("new_expire_time")
    private Long newExpireTime;

    /**
     * 续费状态 1-续费中 2-续费成功 3-续费失败
     */
    @TableField("renewal_status")
    private Boolean renewalStatus;

    /**
     * 续费时间
     */
    @TableField("renewal_time")
    private Long renewalTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
