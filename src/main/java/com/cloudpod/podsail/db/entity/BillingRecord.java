package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 计费记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("billing_record")
public class BillingRecord extends Entity<BillingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 服务器实例ID
     */
    @TableField("instance_id")
    private Long instanceId;

    /**
     * 服务器实例ID(cloudpods)
     */
    @TableField("server_id")
    private String serverId;

    /**
     * 计费类型 1-按需付费 2-包年包月
     */
    @TableField("billing_type")
    private Boolean billingType;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private String billingCycle;

    /**
     * 计费开始时间
     */
    @TableField("start_time")
    private Long startTime;

    /**
     * 计费结束时间
     */
    @TableField("end_time")
    private Long endTime;

    /**
     * 计费时长(秒)
     */
    @TableField("duration")
    private Long duration;

    /**
     * 单价
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 计费金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 货币单位
     */
    @TableField("currency")
    private String currency;

    /**
     * 记录类型 1-正常计费 2-退费 3-补费
     */
    @TableField("record_type")
    private Boolean recordType;

    /**
     * 结算状态 1-未结算 2-已结算
     */
    @TableField("settlement_status")
    private Boolean settlementStatus;

    /**
     * 结算时间
     */
    @TableField("settlement_time")
    private Long settlementTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
