package com.cloudpod.podsail.config;

import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.yunionyun.mcp.mcclient.AuthAgent;
import com.yunionyun.mcp.mcclient.Client;
import com.yunionyun.mcp.mcclient.JSONClientException;
import com.yunionyun.mcp.mcclient.common.McClientJavaBizException;
import com.yunionyun.mcp.mcclient.keystone.TokenCredential;
import com.yunionyun.mcp.mcclient.managers.impl.compute.ServerManager;
import com.yunionyun.mcp.mcclient.managers.impl.k8s.ServiceManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "cloudpods.client")
public class CloudPodClientConfig {

    private String authUrl;
    private String user;
    private String password;
    private String project;
    private String userDomain;
    private String projectDomain;

    @Bean
    public Client mcClient() {
        return new Client(authUrl, 30, true, false);
    }

    @Bean
    public AuthAgent authAgent() {
        AuthAgent authAgent = new AuthAgent(authUrl, userDomain, user, password, project, 1024, 60, true, true);
        authAgent.start_sync_ready();
        authAgent.start();
        return authAgent;
    }
}
