package com.cloudpod.podsail.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * HTTP请求日志配置
 * 打印类似curl的请求命令
 */
@Component
@Slf4j
public class HttpLoggingConfig {

    /**
     * 创建curl风格的请求日志拦截器
     */
    public static ClientHttpRequestInterceptor createCurlLoggingInterceptor() {
        return new ClientHttpRequestInterceptor() {
            @Override
            public ClientHttpResponse intercept(
                    HttpRequest request, 
                    byte[] body, 
                    ClientHttpRequestExecution execution) throws IOException {
                
                logCurlCommand(request, body);
                
                ClientHttpResponse response = execution.execute(request, body);
                
                logResponse(response);
                
                return response;
            }
        };
    }

    private static void logCurlCommand(HttpRequest request, byte[] body) {
        log.info("🌐 =========================== HTTP请求开始 ===========================");
        log.info("🎯 请求方法: {}", request.getMethod());
        log.info("🌍 请求URL: {}", request.getURI());
        log.info("📋 请求头: {}", request.getHeaders());
        
        // 打印完整的请求体
        if (body != null && body.length > 0) {
            String bodyStr = new String(body, StandardCharsets.UTF_8);
            log.info("📤 完整请求体: {}", bodyStr);
        } else {
            log.info("📤 请求体: (空)");
        }
        
        // 生成curl命令
        StringBuilder curl = new StringBuilder();
        curl.append("curl -X ").append(request.getMethod()).append(" ");
        
        // 添加所有请求头
        request.getHeaders().forEach((name, values) -> {
            values.forEach(value -> {
                // 对于敏感信息，只显示部分
                if ("Authorization".equalsIgnoreCase(name) && value.startsWith("Bearer ")) {
                    String token = value.substring(7);
                    String maskedToken = token.length() > 10 ? 
                        token.substring(0, 10) + "..." : "***";
                    curl.append("-H \"").append(name).append(": Bearer ").append(maskedToken).append("\" ");
                } else {
                    curl.append("-H \"").append(name).append(": ").append(value).append("\" ");
                }
            });
        });
        
        // 添加请求体到curl命令（完整版）
        if (body != null && body.length > 0) {
            String bodyStr = new String(body, StandardCharsets.UTF_8);
            curl.append("-d '").append(bodyStr.replace("'", "\\'")).append("' ");
        }
        
        curl.append("\"").append(request.getURI()).append("\"");
        
        log.info("🔍 等价的curl命令:\n{}", curl.toString());
    }

    private static void logResponse(ClientHttpResponse response) throws IOException {
        log.info("📡 =========================== HTTP响应开始 ===========================");
        log.info("🎯 响应状态: {}", response.getStatusCode());
        log.info("📋 响应头: {}", response.getHeaders());
        
        // 读取并打印响应体
        try {
            // 使用BufferingClientHttpRequestFactory确保响应体可以重复读取
            byte[] responseBody = response.getBody().readAllBytes();
            if (responseBody.length > 0) {
                String responseBodyStr = new String(responseBody, StandardCharsets.UTF_8);
                log.info("📄 完整响应体: {}", responseBodyStr);
                log.info("📊 响应体长度: {} 字符", responseBodyStr.length());
            } else {
                log.info("📄 响应体: (空)");
            }
        } catch (Exception e) {
            log.warn("⚠️ 读取响应体失败: {}", e.getMessage());
        }
        
        log.info("🌐 =========================== HTTP请求完成 ===========================");
    }
} 