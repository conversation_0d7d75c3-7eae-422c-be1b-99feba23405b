package com.cloudpod.podsail;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.crypto.digest.DigestUtil;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @className PasswordTests
 * @date 2025/8/18 21:51
 */
public class PasswordTests {

    @Test
    public void test() {
        String admin123456 = DigestUtil.bcrypt("admin@123456");
        System.out.println(
                DigestUtil.bcryptCheck("admin@123456", admin123456)
        );
        String bcrypt = DigestUtil.bcrypt("admin@123456");
        System.out.println(
                DigestUtil.bcryptCheck("admin@123456", bcrypt)
        );
        for (int i = 0; i < 10; i++) {
            System.out.println(
                    BCrypt.gensalt(16)
            );
        }
    }
}
