package com.cloudpod.podsail;

import com.yunionyun.mcp.mcclient.*;
import com.yunionyun.mcp.mcclient.common.McClientJavaBizException;
import com.yunionyun.mcp.mcclient.keystone.TokenCredential;
import com.yunionyun.mcp.mcclient.managers.ListResult;
import com.yunionyun.mcp.mcclient.managers.impl.compute.ServerManager;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;

import java.io.IOException;

/**
 * @Desc CloudPodsClientTests
 * @Date 2025/8/16 21:35
 * <AUTHOR>
 */
@Slf4j
public class CloudPodsClientTests {

    @Test
    public void test() {
        String authUrl = "https://112.31.133.49:30001/api/s/identity/v3";
        String user = "admin";
        String password = "admin@123";
        Client client = new Client(authUrl, 30, true, true);
        try {
            TokenCredential authenticate = client.Authenticate(user, password, "Default", "system", "Default");

            System.out.println(
                    authenticate.getToken()
            );

        } catch (Exception e) {
            log.error("[cloudpods] 客户端初始化失败");
//            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "初始化失败");
        }
    }

    @Test
    public void test1() {
        AuthAgent authAgent = buildAuthAgent();
        Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.PublicURL);
        System.out.println(
                adminSession.getToken().getToken()
        );
        ServerManager serverManager = new ServerManager();
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("limit", 10);
            ListResult list = serverManager.List(adminSession, null);
            System.out.println(
                    list
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public AuthAgent buildAuthAgent() {
        String authUrl = "https://112.31.133.49:30001/api/s/identity/v3";
        String user = "admin";
        String password = "admin@123";
        AuthAgent authAgent = new AuthAgent(authUrl, "Default", user, password, "system", 1024, 60, true, true);
        authAgent.start_sync_ready();
        authAgent.start();
        return authAgent;
    }


}
