### 云平台项目初始化


### CloudPods java SDK 报错问题解决
> 错误描述,原因是Java 9+ 的模块化系统（JPMS）限制导致
>java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.String java.net.HttpURLConnection.method accessible: module java.base does not "opens java.net" to unnamed module @aecb35a


> 启动时加上
```sh
java
 --add-opens java.base/java.net=ALL-UNNAMED
 --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED
 -jar server.jar
```
> idea Edit Config 放在vm的启动参数中


